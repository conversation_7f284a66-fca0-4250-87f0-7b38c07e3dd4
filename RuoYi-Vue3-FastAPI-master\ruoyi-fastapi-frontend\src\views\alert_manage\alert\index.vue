<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="告警类型" prop="alertType">
        <el-select v-model="queryParams.alertType" placeholder="请选择告警类型" clearable style="width: 200px">
          <el-option label="人员入侵" value="person_intrusion" />
          <el-option label="车辆计数" value="car_counting" />
        </el-select>
      </el-form-item>
      <el-form-item label="告警级别" prop="alertLevel">
        <el-select v-model="queryParams.alertLevel" placeholder="请选择告警级别" clearable style="width: 200px">
          <el-option label="低" value="1" />
          <el-option label="中" value="2" />
          <el-option label="高" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable style="width: 200px">
          <el-option label="未处理" value="0" />
          <el-option label="已处理" value="1" />
          <el-option label="已忽略" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据流名称" prop="streamName">
        <el-input
          v-model="queryParams.streamName"
          placeholder="请输入数据流名称"
          clearable
          @keyup.enter="handleQuery"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="告警时间" prop="alertTime">
        <el-date-picker
          v-model="queryParams.alertTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alert_manage:alert:remove']"
        >删除</el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="alertList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="告警类型" align="center" prop="alertType" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.alertType === 'person_intrusion'" type="warning">人员入侵</el-tag>
          <el-tag v-else-if="scope.row.alertType === 'car_counting'" type="info">车辆计数</el-tag>
          <el-tag v-else>{{ scope.row.alertType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="告警级别" align="center" prop="alertLevel" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.alertLevel === '1'" type="success">低</el-tag>
          <el-tag v-else-if="scope.row.alertLevel === '2'" type="warning">中</el-tag>
          <el-tag v-else-if="scope.row.alertLevel === '3'" type="danger">高</el-tag>
          <el-tag v-else>{{ scope.row.alertLevel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据流名称" align="center" prop="streamName" width="150" show-overflow-tooltip />
      <el-table-column label="告警消息" align="center" min-width="320" show-overflow-tooltip>
        <template #default="scope">
          <div class="alert-message-container">
            <!-- 用户配置的告警消息 -->
            <div class="user-alert-message">{{ scope.row.alertMessage || '告警' }}</div>

            <!-- 车辆检测信息 -->
            <div v-if="getVehicleDetectionInfo(scope.row)" class="vehicle-detection-info">
              <div class="detection-summary">
                <span class="info-label">累计总计:</span>
                <span class="info-value">{{ getVehicleDetectionInfo(scope.row).totalAlertCount }}</span>
              </div>
              <div v-if="getVehicleDetectionInfo(scope.row).vehicleIds.length > 0" class="vehicle-ids-inline">
                <span class="info-label">车辆ID:</span>
                <span class="vehicle-ids-text">{{ formatVehicleIds(getVehicleDetectionInfo(scope.row).vehicleIds) }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="置信度" align="center" prop="confidence" width="100">
        <template #default="scope">
          <span v-if="scope.row.confidence">{{ (scope.row.confidence * 100).toFixed(1) }}%</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '0'" type="danger">未处理</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="success">已处理</el-tag>
          <el-tag v-else-if="scope.row.status === '2'" type="info">已忽略</el-tag>
          <el-tag v-else>{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="告警时间" align="center" prop="alertTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.alertTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="截图" align="center" width="80">
        <template #default="scope">
          <el-button v-if="scope.row.screenshotPath" link type="primary" @click="viewImage(scope.row.screenshotPath)">查看</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-dropdown @command="(command) => handleStatusChange(scope.row, command)">
            <el-button link type="primary" size="small">
              状态操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1" v-if="scope.row.status !== '1'">标记已处理</el-dropdown-item>
                <el-dropdown-item command="2" v-if="scope.row.status !== '2'">标记已忽略</el-dropdown-item>
                <el-dropdown-item command="0" v-if="scope.row.status !== '0'">标记未处理</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['alert_manage:alert:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 图片查看对话框 -->
    <el-dialog title="告警截图" v-model="imageDialogVisible" width="800px" append-to-body>
      <div style="text-align: center;">
        <img :src="currentImageUrl" style="max-width: 100%; max-height: 500px;" alt="告警截图" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Alert">
import { listAlert, delAlert, updateAlertStatus } from "@/api/alert_manage/alert";

const { proxy } = getCurrentInstance();

const alertList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const total = ref(0);

// 图片查看相关
const imageDialogVisible = ref(false);
const currentImageUrl = ref("");

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    alertType: null,
    alertLevel: null,
    status: null,
    alertTime: null,
    streamName: null,
  }
});

const { queryParams } = toRefs(data);

/** 查询警告记录列表 */
function getList() {
  loading.value = true;

  // 处理查询参数，特别是时间范围
  const params = { ...queryParams.value };

  // 处理时间范围参数
  if (params.alertTime && Array.isArray(params.alertTime) && params.alertTime.length === 2) {
    // 将数组格式转换为逗号分隔的字符串
    params.alertTime = params.alertTime.join(',');
  } else if (!params.alertTime || (Array.isArray(params.alertTime) && params.alertTime.length === 0)) {
    // 如果时间范围为空，删除该参数
    delete params.alertTime;
  }

  listAlert(params).then(response => {
    alertList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error('获取告警列表失败:', error);
    loading.value = false;
  });
}

/** 查看图片 */
function viewImage(imagePath) {
  if (imagePath) {
    // 如果是相对路径，添加基础URL
    if (imagePath.startsWith('/profile/')) {
      currentImageUrl.value = import.meta.env.VITE_APP_BASE_API + imagePath;
    } else if (imagePath.startsWith('http')) {
      // 如果是完整URL，直接使用
      currentImageUrl.value = imagePath;
    } else {
      // 其他情况，添加基础URL
      currentImageUrl.value = import.meta.env.VITE_APP_BASE_API + '/' + imagePath.replace(/^\/+/, '');
    }
    imageDialogVisible.value = true;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.alertId);
  multiple.value = !selection.length;
}

/** 状态变更操作 */
function handleStatusChange(row, newStatus) {
  const statusText = {
    '0': '未处理',
    '1': '已处理',
    '2': '已忽略'
  };

  proxy.$modal.confirm(`确认将告警状态变更为"${statusText[newStatus]}"？`).then(function() {
    const updateData = {
      alertId: row.alertId,
      status: newStatus,
      handleRemark: `状态变更为${statusText[newStatus]}`
    };

    // 调用状态更新API
    return updateAlertStatus(updateData);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("状态更新成功");
  }).catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _alertIds = row.alertId || ids.value;
  proxy.$modal.confirm('是否确认删除告警记录编号为"' + _alertIds + '"的数据项？').then(function() {
    return delAlert(_alertIds);
  }).then((response) => {
    // 检查响应结果和消息内容
    if (response && response.code === 200) {
      // 检查消息内容，如果包含"没有找到"或"失败"等关键词，视为失败
      const msg = response.msg || "";
      if (msg.includes("没有找到") || msg.includes("失败") || msg.includes("错误")) {
        proxy.$modal.msgError(msg);
      } else {
        getList();
        proxy.$modal.msgSuccess(msg || "删除成功");
      }
    } else {
      proxy.$modal.msgError(response.msg || "删除失败");
    }
  }).catch((error) => {
    // 检查是否是用户取消操作
    if (error === 'cancel' || error === 'close' || error.message === 'cancel') {
      // 用户取消，不显示错误消息
      console.log('用户取消删除告警记录');
      return;
    }

    console.error('删除告警记录异常:', error);
    proxy.$modal.msgError("删除告警记录异常: " + (error.message || error));
  });
}

/** 获取车辆检测信息（整合计数和ID信息） */
function getVehicleDetectionInfo(row) {
  try {
    if (!row.bboxInfo) return null;

    const bboxInfo = typeof row.bboxInfo === 'string' ? JSON.parse(row.bboxInfo) : row.bboxInfo;

    let vehicleCount = 0;
    let uniqueVehicleCount = 0;
    let totalAlertCount = 0;
    let vehicleIds = [];

    // 检查是否有增强的bbox_info格式
    if (bboxInfo.vehicle_tracking && bboxInfo.counting_statistics) {
      vehicleCount = bboxInfo.vehicle_tracking.vehicle_count || 0;
      uniqueVehicleCount = bboxInfo.vehicle_tracking.unique_vehicle_count || 0;
      totalAlertCount = bboxInfo.counting_statistics.total_alert_count || 0;
      vehicleIds = bboxInfo.vehicle_tracking.vehicle_ids?.filter(id => id !== null && id !== undefined) || [];
    }
    // 兼容旧格式
    else if (bboxInfo.details) {
      const details = bboxInfo.details;
      vehicleCount = details.detections ? details.detections.length : 0;
      totalAlertCount = details.total_alert_count || 0;

      if (details.detections) {
        const trackIds = details.detections
          .map(det => det.track_id)
          .filter(id => id !== null && id !== undefined);
        vehicleIds = [...new Set(trackIds)]; // 去重
        uniqueVehicleCount = vehicleIds.length;
      }
    }
    // 更旧的格式兼容
    else if (bboxInfo.detections) {
      vehicleCount = bboxInfo.detections.length;
      const trackIds = bboxInfo.detections
        .map(det => det.track_id)
        .filter(id => id !== null && id !== undefined);
      vehicleIds = [...new Set(trackIds)]; // 去重
      uniqueVehicleCount = vehicleIds.length;
    }

    // 如果没有检测到车辆，返回null
    if (vehicleCount === 0 && vehicleIds.length === 0) {
      return null;
    }

    // 构建摘要信息
    let summary = `${vehicleCount}辆`;
    if (uniqueVehicleCount > 0 && uniqueVehicleCount !== vehicleCount) {
      summary += ` (唯一:${uniqueVehicleCount}辆)`;
    }
    if (totalAlertCount > 0) {
      summary += ` 总计:${totalAlertCount}`;
    }

    return {
      vehicleCount,
      uniqueVehicleCount,
      totalAlertCount,
      vehicleIds,
      summary
    };

  } catch (error) {
    console.error('解析车辆检测信息失败:', error);
    return null;
  }
}

/** 格式化车辆ID显示 */
function formatVehicleIds(vehicleIds) {
  if (!vehicleIds || vehicleIds.length === 0) {
    return '-';
  }

  // 去重并排序
  const uniqueIds = [...new Set(vehicleIds)].sort((a, b) => a - b);

  // 如果ID数量较少，直接显示
  if (uniqueIds.length <= 8) {
    return uniqueIds.join(', ');
  }

  // 如果ID数量较多，显示前几个和总数
  const displayIds = uniqueIds.slice(0, 6);
  return `${displayIds.join(', ')} 等${uniqueIds.length}个`;
}

getList();
</script>

<style scoped>
.alert-message-container {
  text-align: left;
  padding: 4px 0;
}

.user-alert-message {
  font-weight: 600;
  color: #e6a23c;
  margin-bottom: 6px;
  font-size: 13px;
  background-color: #fdf6ec;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.vehicle-detection-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 12px;
  border-left: 3px solid #409eff;
}

.detection-summary {
  margin-bottom: 4px;
}

.vehicle-ids-inline {
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.info-value {
  color: #409eff;
  font-weight: bold;
}

.vehicle-ids-text {
  color: #67c23a;
  font-weight: 500;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e1f5fe;
}

.fallback-message {
  color: #606266;
  font-style: italic;
}
</style>